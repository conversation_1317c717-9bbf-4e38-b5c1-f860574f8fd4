import {
  blue_default,
  common_default,
  green_default,
  grey_default,
  lightBlue_default,
  orange_default,
  purple_default,
  red_default
} from "./chunk-64FVIM6J.js";
import {
  __export
} from "./chunk-G3PMV62Z.js";

// node_modules/@mui/material/esm/colors/index.js
var colors_exports = {};
__export(colors_exports, {
  amber: () => amber_default,
  blue: () => blue_default,
  blueGrey: () => blueGrey_default,
  brown: () => brown_default,
  common: () => common_default,
  cyan: () => cyan_default,
  deepOrange: () => deepOrange_default,
  deepPurple: () => deepPurple_default,
  green: () => green_default,
  grey: () => grey_default,
  indigo: () => indigo_default,
  lightBlue: () => lightBlue_default,
  lightGreen: () => lightGreen_default,
  lime: () => lime_default,
  orange: () => orange_default,
  pink: () => pink_default,
  purple: () => purple_default,
  red: () => red_default,
  teal: () => teal_default,
  yellow: () => yellow_default
});

// node_modules/@mui/material/esm/colors/pink.js
var pink = {
  50: "#fce4ec",
  100: "#f8bbd0",
  200: "#f48fb1",
  300: "#f06292",
  400: "#ec407a",
  500: "#e91e63",
  600: "#d81b60",
  700: "#c2185b",
  800: "#ad1457",
  900: "#880e4f",
  A100: "#ff80ab",
  A200: "#ff4081",
  A400: "#f50057",
  A700: "#c51162"
};
var pink_default = pink;

// node_modules/@mui/material/esm/colors/deepPurple.js
var deepPurple = {
  50: "#ede7f6",
  100: "#d1c4e9",
  200: "#b39ddb",
  300: "#9575cd",
  400: "#7e57c2",
  500: "#673ab7",
  600: "#5e35b1",
  700: "#512da8",
  800: "#4527a0",
  900: "#311b92",
  A100: "#b388ff",
  A200: "#7c4dff",
  A400: "#651fff",
  A700: "#6200ea"
};
var deepPurple_default = deepPurple;

// node_modules/@mui/material/esm/colors/indigo.js
var indigo = {
  50: "#e8eaf6",
  100: "#c5cae9",
  200: "#9fa8da",
  300: "#7986cb",
  400: "#5c6bc0",
  500: "#3f51b5",
  600: "#3949ab",
  700: "#303f9f",
  800: "#283593",
  900: "#1a237e",
  A100: "#8c9eff",
  A200: "#536dfe",
  A400: "#3d5afe",
  A700: "#304ffe"
};
var indigo_default = indigo;

// node_modules/@mui/material/esm/colors/cyan.js
var cyan = {
  50: "#e0f7fa",
  100: "#b2ebf2",
  200: "#80deea",
  300: "#4dd0e1",
  400: "#26c6da",
  500: "#00bcd4",
  600: "#00acc1",
  700: "#0097a7",
  800: "#00838f",
  900: "#006064",
  A100: "#84ffff",
  A200: "#18ffff",
  A400: "#00e5ff",
  A700: "#00b8d4"
};
var cyan_default = cyan;

// node_modules/@mui/material/esm/colors/teal.js
var teal = {
  50: "#e0f2f1",
  100: "#b2dfdb",
  200: "#80cbc4",
  300: "#4db6ac",
  400: "#26a69a",
  500: "#009688",
  600: "#00897b",
  700: "#00796b",
  800: "#00695c",
  900: "#004d40",
  A100: "#a7ffeb",
  A200: "#64ffda",
  A400: "#1de9b6",
  A700: "#00bfa5"
};
var teal_default = teal;

// node_modules/@mui/material/esm/colors/lightGreen.js
var lightGreen = {
  50: "#f1f8e9",
  100: "#dcedc8",
  200: "#c5e1a5",
  300: "#aed581",
  400: "#9ccc65",
  500: "#8bc34a",
  600: "#7cb342",
  700: "#689f38",
  800: "#558b2f",
  900: "#33691e",
  A100: "#ccff90",
  A200: "#b2ff59",
  A400: "#76ff03",
  A700: "#64dd17"
};
var lightGreen_default = lightGreen;

// node_modules/@mui/material/esm/colors/lime.js
var lime = {
  50: "#f9fbe7",
  100: "#f0f4c3",
  200: "#e6ee9c",
  300: "#dce775",
  400: "#d4e157",
  500: "#cddc39",
  600: "#c0ca33",
  700: "#afb42b",
  800: "#9e9d24",
  900: "#827717",
  A100: "#f4ff81",
  A200: "#eeff41",
  A400: "#c6ff00",
  A700: "#aeea00"
};
var lime_default = lime;

// node_modules/@mui/material/esm/colors/yellow.js
var yellow = {
  50: "#fffde7",
  100: "#fff9c4",
  200: "#fff59d",
  300: "#fff176",
  400: "#ffee58",
  500: "#ffeb3b",
  600: "#fdd835",
  700: "#fbc02d",
  800: "#f9a825",
  900: "#f57f17",
  A100: "#ffff8d",
  A200: "#ffff00",
  A400: "#ffea00",
  A700: "#ffd600"
};
var yellow_default = yellow;

// node_modules/@mui/material/esm/colors/amber.js
var amber = {
  50: "#fff8e1",
  100: "#ffecb3",
  200: "#ffe082",
  300: "#ffd54f",
  400: "#ffca28",
  500: "#ffc107",
  600: "#ffb300",
  700: "#ffa000",
  800: "#ff8f00",
  900: "#ff6f00",
  A100: "#ffe57f",
  A200: "#ffd740",
  A400: "#ffc400",
  A700: "#ffab00"
};
var amber_default = amber;

// node_modules/@mui/material/esm/colors/deepOrange.js
var deepOrange = {
  50: "#fbe9e7",
  100: "#ffccbc",
  200: "#ffab91",
  300: "#ff8a65",
  400: "#ff7043",
  500: "#ff5722",
  600: "#f4511e",
  700: "#e64a19",
  800: "#d84315",
  900: "#bf360c",
  A100: "#ff9e80",
  A200: "#ff6e40",
  A400: "#ff3d00",
  A700: "#dd2c00"
};
var deepOrange_default = deepOrange;

// node_modules/@mui/material/esm/colors/brown.js
var brown = {
  50: "#efebe9",
  100: "#d7ccc8",
  200: "#bcaaa4",
  300: "#a1887f",
  400: "#8d6e63",
  500: "#795548",
  600: "#6d4c41",
  700: "#5d4037",
  800: "#4e342e",
  900: "#3e2723",
  A100: "#d7ccc8",
  A200: "#bcaaa4",
  A400: "#8d6e63",
  A700: "#5d4037"
};
var brown_default = brown;

// node_modules/@mui/material/esm/colors/blueGrey.js
var blueGrey = {
  50: "#eceff1",
  100: "#cfd8dc",
  200: "#b0bec5",
  300: "#90a4ae",
  400: "#78909c",
  500: "#607d8b",
  600: "#546e7a",
  700: "#455a64",
  800: "#37474f",
  900: "#263238",
  A100: "#cfd8dc",
  A200: "#b0bec5",
  A400: "#78909c",
  A700: "#455a64"
};
var blueGrey_default = blueGrey;

export {
  pink_default,
  deepPurple_default,
  indigo_default,
  cyan_default,
  teal_default,
  lightGreen_default,
  lime_default,
  yellow_default,
  amber_default,
  deepOrange_default,
  brown_default,
  blueGrey_default,
  colors_exports
};
//# sourceMappingURL=chunk-WSTFRFKP.js.map
