{"name": "backend", "version": "1.0.0", "description": "", "license": "ISC", "author": "king", "type": "commonjs", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcrypt": "^6.0.0", "body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "nodemailer": "^7.0.4"}, "devDependencies": {"nodemon": "^3.1.10"}}